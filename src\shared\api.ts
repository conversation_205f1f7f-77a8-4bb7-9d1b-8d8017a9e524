import { z } from "zod";
import { services, countries, digitalSocieties } from "./lists";

function nullableToUndefined<T>(schema: z.ZodSchema<T>) {
  return schema.nullable().optional().transform((val) => val ?? undefined);
}

const _ = nullableToUndefined;

export type ExtractRequest = Normalize<z.output<typeof ExtractRequest>>;
export const ExtractRequest = z.object({
  text: z.string(),
});

export type ExtractResponse = Normalize<z.output<typeof ExtractResponse>>;
export const ExtractResponse = z.object({
  service_type: _(z.string()),
  incoterms: _(z.string()),
  origin: _(z.string()),
  destination_country: _(z.string()),

  pickup: _(z.object({
    is_needed: _(z.boolean()),
    city: _(z.string()),
    zip_code: _(z.string()),
    address: _(z.string()),
  })),

  delivery: _(z.object({
    is_needed: _(z.boolean()),
    city: _(z.string()),
    zip_code: _(z.string()),
    address: _(z.string()),
  })),

  summary: z.object({
    piece: _(z.number()),
    weight: _(z.number()),
    volume: _(z.number()),
    density: _(z.number()),
    chargeable_weight: _(z.number()),
  }),

  details: _(z.array(
    z.object({
      piece: _(z.number()),
      dimension: z.object({
        width: z.number().optional(),
        height: z.number().optional(),
        length: z.number().optional(),
        volume: z.number().optional(),
        weight: z.number().optional(),
        is_stackable: z.boolean().optional(),
      }),
    }),
  )),

  additional_details: z.object({
    description_of_goods: _(z.string()),
    hs_codes: _(z.number()),
    costs_of_goods: _(z.number()),
    services: _(z.enum(["express", "standard", "economy"])),
    selected_services: _(z.array(z.string())),
  }),
});

const otp = z.string().length(6).regex(/\d{6}/);

export type OtpRequest = Normalize<z.output<typeof OtpRequest>>;
export const OtpRequest = z.object({
  email: z.string().email(),
});

export type SignUpRequest = Normalize<z.output<typeof SignUpRequest>>;
export const SignUpRequest = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(256),
  country: z.enum([...countries, ...digitalSocieties]),
  otp,
});

export type SignInRequest = Normalize<z.output<typeof SignInRequest>>;
export const SignInRequest = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(256),
});

export type ResetPasswordRequest = Normalize<z.output<typeof ResetPasswordRequest>>;
export const ResetPasswordRequest = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(256),
  otp,
});

export type CreateRequestRequest = Normalize<z.output<typeof CreateRequestRequest>>;
export const CreateRequestRequest = z.object({
  origin: z.string(),
  destination: z.string(),

  service: z.enum(services),

  quantity: z.number().int().positive(),
  weight: z.number().nonnegative(),
  volume: z.number().nonnegative(),
  chargeableWeight: z.number().nonnegative(),
});

export type UpdateRequestStatusRequest = Normalize<z.output<typeof UpdateRequestStatusRequest>>;
export const UpdateRequestStatusRequest = z.object({
  id: z.string().length(21),
  status: z.enum(["pending", "order", "calculating", "rejected"]),
});

export type GetRequestsRequest = Normalize<z.output<typeof GetRequestsRequest>>;
export const GetRequestsRequest = z.object({
  origin: z.string().optional(),
  destination: z.string().optional(),

  service: z.enum(services).optional(),

  from: z.coerce.date().optional(),
  to: z.coerce.date().optional(),
});

export type DeleteRequestRequest = Normalize<z.output<typeof DeleteRequestRequest>>;
export const DeleteRequestRequest = z.object({
  id: z.string().length(21),
});

export type Request = {
  id: string;

  ordinal: number;

  origin: string;
  destination: string;

  service: string;

  quantity: number;
  weight: number;
  volume: number;
  chargeableWeight: number;

  status: "pending" | "order" | "calculating" | "rejected";

  createdAt: Date;
  updatedAt: Date;
}
