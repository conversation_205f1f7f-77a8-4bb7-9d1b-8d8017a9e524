// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model EmailOtp {
  @@map("email_otps")

  id String @id @default(nanoid())

  email String
  otp   String

  expiresAt DateTime @map("expires_at")

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt

  @@index([email, otp])
  @@unique([email, otp])
}

model User {
  @@map("users")

  id String @id @default(nanoid())

  email String @unique
  hash  String

  country String @default("France")

  requests Request[]

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt

  @@index([email])
}

enum RequestStatus {
  pending
  order
  calculating
  rejected
}

model Request {
  @@map("requests")

  id String @id @default(nanoid())

  userId String
  user   User   @relation(fields: [userId], references: [id])

  ordinal Int

  origin      String
  destination String

  service String

  quantity         Int
  weight           Float
  volume           Float
  chargeableWeight Float

  status RequestStatus @default(pending)

  createdAt DateTime  @map("created_at") @default(now())
  updatedAt DateTime  @map("updated_at") @updatedAt
  deletedAt DateTime? @map("deleted_at")
}

model UserRequestCounter {
  @@map("user_request_counters")

  id String @id @default(nanoid())

  userId String @unique

  count Int

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt
}
