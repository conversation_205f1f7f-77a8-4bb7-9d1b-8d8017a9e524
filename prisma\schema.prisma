// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model EmailOtp {
  @@map("email_otps")

  id String @id @default(nanoid())

  email String
  otp   String

  expiresAt DateTime @map("expires_at")

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt

  @@index([email, otp])
  @@unique([email, otp])
}

model User {
  @@map("users")

  id String @id @default(nanoid())

  email String @unique
  hash  String

  country String @default("France")

  requests Request[]

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt

  @@index([email])
}

enum RequestStatus {
  pending
  order
  calculating
  rejected
}

model Request {
  @@map("requests")

  id String @id @default(nanoid())

  userId String @map("user_id")
  user   User   @relation(fields: [userId], references: [id])

  ordinal Int

  text String

  service   String?
  incoterms String?

  originCountry String? @map("origin_country")
  originCity    String? @map("origin_city")
  originAddress String? @map("origin_address")
  originZipcode String? @map("origin_zipcode")

  destinationCountry String? @map("destination_country")
  destinationCity    String? @map("destination_city")
  destinationAddress String? @map("destination_address")
  destinationZipcode String? @map("destination_zipcode")

  isPickupRequired   Boolean @map("is_pickup_required")   @default(false)
  isDeliveryRequired Boolean @map("is_delivery_required") @default(false)

  descriptionOfGoods String? @map("description_of_goods")
  hsCodes            String? @map("hs_codes")

  costOfGoods Decimal? @map("cost_of_goods")
  currency    String?

  additionalServices String[] @map("additional_services")
  dangerousGoods     String[] @map("dangerous_goods")

  quantity         Int
  weight           Float
  volume           Float
  chargeableWeight Float @map("chargeable_weight")

  packages RequestPackage[]

  status RequestStatus @default(pending)

  createdAt DateTime  @map("created_at") @default(now())
  updatedAt DateTime  @map("updated_at") @updatedAt
  deletedAt DateTime? @map("deleted_at")
}

enum RequestPackageType {
  box
  crate
  pallet
}

model RequestPackage {
  @@map("request_packages")

  id String @id @default(nanoid())

  requestId String @map("request_id")
  request   Request @relation(fields: [requestId], references: [id])

  quantity Int
  length   Int
  width    Int
  height   Int
  weight   Float

  type RequestPackageType?

  isStackable Boolean @map("is_stackable") @default(false)
}

model UserRequestCounter {
  @@map("user_request_counters")

  id String @id @default(nanoid())

  userId String @map("user_id") @unique

  count Int

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt
}
