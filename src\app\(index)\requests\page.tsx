import { useEffect, useState } from 'react';
import { useOutletContext } from 'react-router';
import {
  Box,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  IconButton,
  InputAdornment,
  styled
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
// import DeleteIcon from '@mui/icons-material/Delete';
// import EditIcon from '@mui/icons-material/Edit';
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import MenuIcon from '@mui/icons-material/Menu';
import { Requester } from '../../../lib/requester';
import { Modal } from '../../../components';
import { Api, Lists } from '../../../shared';

const StyledButton = styled(Button)({
  color: "white",
  fontSize: "12px",
  lineHeight: "16px",
  textTransform: "none",
  fontWeight: "400",
  borderRadius: "0",
  height: "40px",
});

const FilterButton = styled(Button)({
  fontSize: "12px",
  lineHeight: "16px",
  textTransform: "none",
  fontWeight: "700",
  height: "40px",
  padding: "8px 12px",
});

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return { backgroundColor: 'rgba(179, 179, 179, 0.2)', color: '#707070' };
    case 'order':
      return { backgroundColor: 'rgba(112, 181, 125, 0.2)', color: '#70B57D' };
    case 'calculating':
      return { backgroundColor: 'rgba(255, 165, 0, 0.2)', color: '#FFA500' };
    case 'rejected':
      return { backgroundColor: 'rgba(254, 140, 140, 0.44)', color: '#E84B4B' };
    default:
      return { backgroundColor: 'rgba(179, 179, 179, 0.2)', color: '#707070' };
  }
};

interface OutletContext {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
}

export function RequestsPage() {
  const { isSidebarOpen, toggleSidebar } = useOutletContext<OutletContext>();
  const [searchValue, setSearchValue] = useState('');

  const [modalError, setModalError] = useState<string | null>(null);

  const [availableLocations, setAvailableLocations] = useState<{
    origins: string[];
    destinations: string[];
  } | null>(null);

  useEffect(() => {
    async function fetchAvailableLocations() {
      const response = await Requester.get("/request/locations");

      if (response.success) {
        setAvailableLocations(response.body as {
          origins: string[];
          destinations: string[];
        });

        return;
      }

      if (response.error) {
        setModalError(response.error.message);

        return;
      }

      setModalError(`${response.status}: ${response.body}`);
    }

    fetchAvailableLocations();
  }, []);

  const [origin, setOrigin] = useState<string | null>(null);
  const [destination, setDestination] = useState<string | null>(null);
  const [service, setService] = useState<typeof Lists.services[number] | null>(null);

  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);

  const [requests, setRequests] = useState<Api.Request[] | null>(null);

  useEffect(() => {
    let isCanceled = false;

    async function fetchRequests() {
      const url = new URL("/request", window.location.origin);

      if (origin) url.searchParams.set("origin", origin);
      if (destination) url.searchParams.set("destination", destination);
      if (service) url.searchParams.set("service", service);

      if (fromDate) url.searchParams.set("from", fromDate.toISOString());
      if (toDate) url.searchParams.set("to", toDate.toISOString());

      const response = await Requester.get(url.pathname + url.search, {
        cache: "no-store"
      });

      if (isCanceled) return;

      if (response.success) {
        setRequests((response.body as Api.Request[]).map(request => ({
          ...request,
          createdAt: new Date(request.createdAt),
        })));

        return;
      }

      if (response.error) {
        setModalError(response.error.message);

        return;
      }

      setModalError(`${response.status}: ${response.body}`);
    };

    fetchRequests();

    return () => {
      isCanceled = true;
    };
  }, [origin, destination, service, fromDate, toDate]);

  function formatDate(date: Date) {
    return date.toLocaleDateString(navigator.languages[0], {
      month: '2-digit',
      day: '2-digit',
    });
  }

  async function handleStatusChange(id: string, status: Api.Request["status"]) {
    console.log({ id, status });

    const response = await Requester.post(`/request/${id}/status/${status}`, {});

    if (response.success) {
      setRequests(requests?.map(request => request.id === id ? { ...request, status } : request) ?? null);
    }
  }

  return (
    <>
      <Modal
        open={modalError !== null}
        onClose={() => {
          setModalError(null);
        }}
        message={modalError}
      />
      <Box sx={{
        padding: '35px 16px',
        backgroundColor: '#FFFFFF',
        minHeight: '100vh',
        width: '100%'
      }}>
        {/* Header Section */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '27px'
        }}>
          {/* Left Side - Sidebar Toggle + Search */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* Sidebar Toggle Button */}
            <IconButton
              onClick={toggleSidebar}
              sx={{
                width: '30px',
                height: '30px',
                visibility: isSidebarOpen ? 'hidden' : 'visible',
                marginLeft: isSidebarOpen ? '-46px' : '0',
                transition: 'margin-left 0.1s ease-in-out',
                color: '#1B1D1F'
              }}
            >
              <MenuIcon />
            </IconButton>

            {/* Search Input */}
            <TextField
              placeholder="Search"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              sx={{
                width: '729px',
                height: '40px',
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#F7F7F7',
                  height: '40px',
                  fontSize: '16px',
                  fontWeight: 300,
                  color: 'rgba(44, 42, 41, 0.5)',
                  border: 'none',
                  '& fieldset': {
                    border: 'none',
                  },
                }
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: '#1A1A1A' }} />
                    </InputAdornment>
                  ),
                }
              }}
            />
          </Box>

          {/* Action Buttons - Right Side */}
          <Box sx={{ display: 'flex', gap: '16px' }}>
            <StyledButton
              sx={{
                backgroundColor: "rgba(112, 181, 125, 1)",
                width: "186px",
              }}
              startIcon={<AddIcon />}
            >
              Create new request
            </StyledButton>
            <StyledButton
              sx={{
                border: "1px solid rgba(26, 26, 26, 1)",
                width: "92px",
                color: "black",
                backgroundColor: "transparent"
              }}
            >
              Sign in
            </StyledButton>
          </Box>
        </Box>

        {/* Filters Section */}
        <Box sx={{
          display: 'flex',
          gap: '20px',
          marginBottom: '27px',
          alignItems: 'flex-end'
        }}>
          {/* Origin Filter */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px', width: '205px' }}>
            <Box sx={{
              fontSize: '12px',
              fontWeight: 400,
              color: 'rgba(44, 42, 41, 0.5)',
              fontFamily: "'YS Text', sans-serif"
            }}>
              Origin
            </Box>
            <FormControl fullWidth>
              <Select
                value={origin ?? ""}
                onChange={(e) => setOrigin(e.target.value)}
                MenuProps={{
                  disableScrollLock: true,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  PaperProps: {
                    style: {
                      maxHeight: 200,
                    },
                  },
                }}
                sx={{
                  height: '40px',
                  backgroundColor: '#F7F7F7',
                  fontSize: '16px',
                  fontWeight: 300,
                  color: 'rgba(44, 42, 41, 0.5)',
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '& .MuiSelect-icon': {
                    transform: 'rotate(0deg)',
                    color: '#1A1A1A'
                  }
                }}
              >
                <MenuItem value={undefined}>All</MenuItem>
                {availableLocations?.origins.map((origin) => (
                  <MenuItem key={origin} value={origin}>{origin}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Destination Filter */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px', width: '205px' }}>
            <Box sx={{
              fontSize: '12px',
              fontWeight: 400,
              color: 'rgba(44, 42, 41, 0.5)',
              fontFamily: "'YS Text', sans-serif"
            }}>
              Destination
            </Box>
            <FormControl fullWidth>
              <Select
                value={destination ?? ""}
                onChange={(e) => setDestination(e.target.value)}
                MenuProps={{
                  disableScrollLock: true,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  PaperProps: {
                    style: {
                      maxHeight: 200,
                    },
                  },
                }}
                sx={{
                  height: '40px',
                  backgroundColor: '#F7F7F7',
                  fontSize: '16px',
                  fontWeight: 300,
                  color: 'rgba(44, 42, 41, 0.5)',
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '& .MuiSelect-icon': {
                    transform: 'rotate(0deg)',
                    color: '#1A1A1A'
                  }
                }}
              >
                <MenuItem value={undefined}>All</MenuItem>
                {availableLocations?.destinations.map((destination) => (
                  <MenuItem key={destination} value={destination}>{destination}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Service Filter */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px', width: '205px' }}>
            <Box sx={{
              fontSize: '12px',
              fontWeight: 400,
              color: 'rgba(44, 42, 41, 0.5)',
              fontFamily: "'YS Text', sans-serif"
            }}>
              Service
            </Box>
            <FormControl fullWidth>
              <Select
                value={service ?? ""}
                onChange={(e) => setService(e.target.value as typeof Lists.services[number])}
                MenuProps={{
                  disableScrollLock: true,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  PaperProps: {
                    style: {
                      maxHeight: 200,
                    },
                  },
                }}
                sx={{
                  height: '40px',
                  backgroundColor: '#F7F7F7',
                  fontSize: '16px',
                  fontWeight: 300,
                  color: 'rgba(44, 42, 41, 0.5)',
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  '& .MuiSelect-icon': {
                    transform: 'rotate(0deg)',
                    color: '#1A1A1A'
                  }
                }}
              >
                <MenuItem value={undefined}>All</MenuItem>
                <MenuItem value="air">Air freight</MenuItem>
                <MenuItem value="sea">Sea freight</MenuItem>
                <MenuItem value="rail">Rail freight</MenuItem>
                <MenuItem value="road">Road freight</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* Date Period Section */}
        <Box sx={{
          display: 'flex',
          gap: '13px',
          marginBottom: '40px',
          alignItems: 'center'
        }}>
          {/* Date Range Button */}
          <FilterButton
            sx={{
              backgroundColor: '#70B57D',
              color: '#EFEFEF',
              width: '128px',
              gap: '10px',
              whiteSpace: 'nowrap',
            }}
            startIcon={<CalendarTodayIcon sx={{ color: '#EFEFEF' }} />}
          >
            {
              (fromDate && toDate && fromDate.getFullYear() === toDate.getFullYear() && fromDate.getMonth() === toDate.getMonth() && fromDate.getDate() === toDate.getDate())
                ? formatDate(fromDate)
                : <>
                  {fromDate ? formatDate(fromDate) : ""} - {toDate ? formatDate(toDate) : ""}
                </>
            }
          </FilterButton>

          {/* Quick Date Buttons */}
          <FilterButton
            sx={{
              backgroundColor: '#EFEFEF',
              color: '#1A1A1A',
              width: '61px',
            }}
            onClick={() => {
              // from start of today to end of today
              const start = new Date(Date.now());
              start.setHours(0, 0, 0, 0);

              const end = new Date(Date.now());
              end.setHours(23, 59, 59, 999);

              setFromDate(start);
              setToDate(end);
            }}
          >
            Today
          </FilterButton>

          <FilterButton
            sx={{
              backgroundColor: '#EFEFEF',
              color: '#1A1A1A',
              width: '90px'
            }}
            onClick={() => {
              // from start of this week to end of this week
              const now = new Date();
              const dayOfWeek = now.getDay() || 7;

              const start = new Date(now);
              start.setDate(start.getDate() - (dayOfWeek - 1));
              start.setHours(0, 0, 0, 0);

              const end = new Date(now);
              end.setDate(end.getDate() - (dayOfWeek - 1) + 6);
              end.setHours(23, 59, 59, 999);

              setFromDate(start);
              setToDate(end);
            }}
          >
            This week
          </FilterButton>

          <FilterButton
            sx={{
              backgroundColor: '#EFEFEF',
              color: '#1A1A1A',
              width: '97px'
            }}
            onClick={() => {
              // from start of this month to end of this month
              const now = new Date();

              const start = new Date(now);
              start.setDate(1);
              start.setHours(0, 0, 0, 0);

              const end = new Date(now);
              end.setMonth(end.getMonth() + 1);
              end.setDate(0);
              end.setHours(23, 59, 59, 999);

              setFromDate(start);
              setToDate(end);
            }}
          >
            This month
          </FilterButton>
        </Box>

        {/* Table Section */}
        <Box sx={{
          backgroundColor: '#FFFFFF',
          borderRadius: '8px 8px 0px 0px',
          overflow: 'hidden'
        }}>
          {/* Table Header */}
          <Box sx={{
            backgroundColor: '#70B57D',
            height: '66px',
            display: 'flex',
            alignItems: 'center',
            padding: '0 12px',
            borderRadius: '8px 8px 0px 0px'
          }}>
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              gap: '12px'
            }}>
              {/* Column Headers */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                width: '884px'
              }}>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Request date
                </Box>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Request id
                </Box>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Origin
                </Box>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Destination
                </Box>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Quantity
                </Box>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Weight
                </Box>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Volume
                </Box>
                <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                  Chargeable weight
                </Box>
              </Box>

              {/* Actions Header */}
              <Box sx={{ width: '217px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Status
                {/* & Actions */}
              </Box>
            </Box>
          </Box>

          {/* Table Body */}
          <Box>
            {requests?.map((request, index) => (
              <Box key={request.id}>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '0 12px',
                  height: '54px',
                  gap: '12px'
                }}>
                  {/* Data Columns */}
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    width: '884px'
                  }}>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                      {request.createdAt.toLocaleDateString()}
                    </Box>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                      {request.ordinal}
                    </Box>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                      {request.origin}
                    </Box>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif", lineHeight: '16px' }}>
                      {request.destination}
                    </Box>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                      {request.quantity}
                    </Box>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                      {request.weight}
                    </Box>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                      {request.volume}
                    </Box>
                    <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                      {request.chargeableWeight}
                    </Box>
                  </Box>

                  {/* Actions Column */}
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '22px',
                    width: '217px'
                  }}>
                    {/* Status Select */}
                    <FormControl sx={{ minWidth: '79px' }}>
                      <Select
                        value={request.status}
                        onChange={(e) => {
                          e.preventDefault();
                          e.stopPropagation();

                          handleStatusChange(request.id, e.target.value as Api.Request["status"]);
                        }}
                        MenuProps={{
                          disableScrollLock: true,
                          anchorOrigin: {
                            vertical: 'bottom',
                            horizontal: 'left',
                          },
                          transformOrigin: {
                            vertical: 'top',
                            horizontal: 'left',
                          },
                          PaperProps: {
                            style: {
                              maxHeight: 200,
                            },
                            sx: {
                              backgroundColor: '#FFFFFF',
                              padding: '4px',
                              '& .MuiMenuItem-root': {
                                fontSize: '12px',
                                fontWeight: 700,
                                fontFamily: "'YS Text', sans-serif",
                                minHeight: '24px',
                                width: '79px',
                                margin: '2px 0',
                                borderRadius: '0px',
                                padding: '2px 8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                '&[data-value="pending"]': {
                                  backgroundColor: 'rgba(179, 179, 179, 0.2)',
                                  color: '#707070',
                                  '&:hover': {
                                    backgroundColor: 'rgba(179, 179, 179, 0.2)',
                                    outline: '1px solid #707070',
                                  },
                                },
                                '&[data-value="order"]': {
                                  backgroundColor: 'rgba(112, 181, 125, 0.2)',
                                  color: '#70B57D',
                                  '&:hover': {
                                    backgroundColor: 'rgba(112, 181, 125, 0.2)',
                                    outline: '1px solid #70B57D',
                                  },
                                },
                                '&[data-value="calculating"]': {
                                  backgroundColor: 'rgba(255, 165, 0, 0.2)',
                                  color: '#FFA500',
                                  '&:hover': {
                                    backgroundColor: 'rgba(255, 165, 0, 0.2)',
                                    outline: '1px solid #FFA500',
                                  },
                                },
                                '&[data-value="rejected"]': {
                                  backgroundColor: 'rgba(254, 140, 140, 0.44)',
                                  color: '#E84B4B',
                                  '&:hover': {
                                    backgroundColor: 'rgba(254, 140, 140, 0.44)',
                                    outline: '1px solid #E84B4B',
                                  },
                                },
                              },
                            },
                          },
                        }}
                        sx={{
                          ...getStatusColor(request.status),
                          fontSize: '12px',
                          fontWeight: 700,
                          fontFamily: "'YS Text', sans-serif",
                          height: '24px',
                          borderRadius: '0px',
                          padding: '0 8px',
                          '& .MuiSelect-select': {
                            padding: '2px 8px',
                            display: 'flex',
                            alignItems: 'center',
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none',
                          },
                          '& .MuiSelect-icon': {
                            color: 'inherit',
                            fontSize: '16px'
                          }
                        }}
                      >
                        <MenuItem value="pending">pending</MenuItem>
                        <MenuItem value="order">order</MenuItem>
                        <MenuItem value="calculating">calculating</MenuItem>
                        <MenuItem value="rejected">rejected</MenuItem>
                      </Select>
                    </FormControl>

                    {/* Action Buttons */}
                    {/* <IconButton size="small" sx={{ color: '#1B1D1F' }}>
                      <DeleteIcon />
                    </IconButton>
                    <IconButton size="small" sx={{ color: '#1B1D1F' }}>
                      <EditIcon />
                    </IconButton>
                    <IconButton size="small" sx={{ color: '#C6C5CA' }}>
                      <ExpandMoreIcon />
                    </IconButton> */}
                  </Box>
                </Box>

                {/* Divider */}
                {index < requests.length - 1 && (
                  <Box sx={{
                    height: '1px',
                    backgroundColor: '#EFEFEF',
                    width: '100%'
                  }} />
                )}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </>
  );
}