import type { Logger } from "pino";
import type { PrismaClient } from "@prisma/client";
import type { Config } from "../config";
import type { UserService } from "../user";
import type { EmailService } from "../email";

import { z } from "zod";
import bcrypt from "bcrypt";
import { Injectable } from "../utils";
import { AlreadyExistsSignalError, BadRequestSignalError } from "../error/http";

const HASH_SALT_ROUNDS = 10;

export class AuthService extends Injectable {
    private readonly config!: Config;
    private readonly prisma!: PrismaClient;
    private readonly logger!: Logger;
    private readonly userService!: UserService;
    private readonly emailService!: EmailService;

    private otpExpiresIn!: number;
    private bypassOtpCheck!: boolean;
    private disableOtpEmails!: boolean;

    override async init1() {
        this.otpExpiresIn = this.config.getWithValidation("OTP_EXPIRES_IN_MINUTES", z.coerce.number().int().positive());
        this.bypassOtpCheck = this.config.getWithValidation("OTP_BYPASS_CHECK", z.coerce.boolean());
        this.disableOtpEmails = this.config.getWithValidation("OTP_DISABLE_EMAILS", z.coerce.boolean());
    }

    private async checkIfEmailIsRegistered(email: string) {
        if (await this.userService.getByEmail(email)) {
            throw new AlreadyExistsSignalError("email already registered");
        }
    }

    private generateOtp() {
        return Math.floor(Math.random() * 1e6).toString().padStart(6, "0");
    }

    async otp(dto: {
        email: string;
        failOnAlreadyRegistered?: boolean;
    }) {
        if (dto.failOnAlreadyRegistered) {
            await this.checkIfEmailIsRegistered(dto.email);
        }

        const otp = this.generateOtp();

        if (!this.disableOtpEmails) {
            await this.emailService.send({
                to: [dto.email],
                subject: "OTP",
                text: `Your LogiSpot OTP is ${otp}.`,
            });
        }

        await this.prisma.emailOtp.create({
            data: {
                email: dto.email,
                otp,
                expiresAt: new Date(Date.now() + this.otpExpiresIn * 60 * 1000),
            },
        });

        this.logger.info(`For email ${dto.email}, generated OTP ${otp}`);
    }

    async signUp(dto: {
        email: string;
        password: string;
        country: string;
        otp: string;
    }) {
        if (!this.bypassOtpCheck) {
            const emailOtp = await this.prisma.emailOtp.findUnique({
                where: {
                    email_otp: {
                        email: dto.email,
                        otp: dto.otp,
                    },
                    expiresAt: {
                        gt: new Date(),
                    },
                },
            });

            if (!emailOtp) {
                throw new BadRequestSignalError("invalid otp");
            }
        }

        const hash = await bcrypt.hash(dto.password, HASH_SALT_ROUNDS);

        const user = await this.userService.create({
            email: dto.email,
            hash,
        });

        return user;
    }

    async signIn(dto: {
        email: string;
        password: string;
    }) {
        const user = await this.userService.getByEmail(dto.email);

        if (!user || !await bcrypt.compare(dto.password, user.hash)) {
            throw new BadRequestSignalError("invalid credentials");
        }

        return user;
    }

    async resetPassword(dto: {
        email: string;
        password: string;
        otp: string;
    }) {
        if (!this.bypassOtpCheck) {
            const emailOtp = await this.prisma.emailOtp.findUnique({
                where: {
                    email_otp: {
                        email: dto.email,
                        otp: dto.otp,
                    },
                    expiresAt: {
                        gt: new Date(),
                    },
                },
            });

            if (!emailOtp) {
                throw new BadRequestSignalError("invalid otp");
            }
        }

        const hash = await bcrypt.hash(dto.password, HASH_SALT_ROUNDS);

        await this.userService.updateHash({
            email: dto.email,
            hash,
        });
    }
}
