import { Outlet } from "react-router";
import { Box, CssBaseline, Typography } from "@mui/material";

export function AuthLayout() {
  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      width: '100%',
      overflowX: 'hidden', // Prevent horizontal scrolling
    }}>
      <CssBaseline />

      {/* Left side - Image background */}
      <Box
        sx={{
          width: '35%',
          flexBasis: '35%',
          flexShrink: 0,
          background: 'linear-gradient(180deg, #F5F9FF 0%, #C3CBD9 100%)',
          display: { xs: 'none', md: 'block' },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Image overlay */}
        <img
          src="/images/auth-layout-sidebar-background-cut.png"
          alt="Background"
          style={{
            width: '85%',
            height: 'auto',
            marginTop: "25vh",
          }}
        />
      </Box>

      {/* Right side - Content */}
      <Box
        sx={{
          width: { xs: '100%', md: '65%' },
          flexBasis: { xs: '100%', md: '65%' },
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'start',
          justifyContent: 'left',
          p: { xs: 3, sm: 4, md: 5 },
          ml: { xs: 0, md: '5vw' },
          overflowY: 'auto', // Enable vertical scrolling if needed
          boxSizing: 'border-box', // Include padding in width calculation
        }}
      >
        {/* Logo and App Title Header */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 5, width: '100%' }}>
          <Box
            sx={{
              width: 44,
              height: 44,
              borderRadius: "50%",
              bgcolor: "#1A1A1A",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mr: 1,
            }}
          >
            <img
              src="/images/logo-white-on-black.svg"
              alt="LogiSpot Logo"
              style={{
                width: '48px',
                height: '48px'
              }}
            />
          </Box>
          <Typography
            variant="h5"
            component="div"
            sx={{
              letterSpacing: '0px',
              fontWeight: 700,
              color: "#1A1A1A",
              fontFamily: "'YS Text Bold', sans-serif"
            }}
          >
            LOGISPOT
          </Typography>
        </Box>

        <Box sx={{ width: 'fit-content' }}>
          <Outlet />
        </Box>
      </Box>
    </Box>
  );
}
