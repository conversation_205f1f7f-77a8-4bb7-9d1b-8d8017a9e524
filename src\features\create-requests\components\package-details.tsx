import React, { MouseEvent, useCallback } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { Base, Detail, SummaryFieldKey } from "../types/shippingTypes";
import useSubscribeToPackageUpdates from "../utils/useSubscribeToPackageUpdates";
import styles from "./package-details.module.css";

import { PackageItem } from ".";

const emptyPackage: Detail = {
  piece: 1,
  package_type: "",
  dimension: {
    width: 0,
    height: 0,
    length: 0,
    volume: 0,
    weight: 0,
    is_stackable: false,
  },
};

type Props = {
  base: Base;
  packageOptions: string[];
  handleValueUpdate: (fieldKey: SummaryFieldKey) => void;
};

export const PackageDetails = React.memo(({
  base,
  packageOptions,
  handleValueUpdate,
}: Props) => {
  const { fields, append, remove } = useFieldArray({
    name: "details",
  });
  const { trigger, getValues, setValue } = useFormContext();
  const { handleRemove, handlePackageAddition } =
    useSubscribeToPackageUpdates();

  const handleAddPackage = (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    append(emptyPackage);
    handlePackageAddition();
  };

  const handlePackageRemove = useCallback(
    (index: number) => {
      remove(index);
      handleRemove(base);
    },
    [remove, handleRemove, base]
  );

  return (
    <div>
      <h3>Package Details</h3>

      <div className={styles.packageDetailsSectionsContainer}>
        {fields.map((field, index) => (
          <PackageItem
            options={packageOptions}
            key={field.id}
            index={index}
            getValues={getValues}
            trigger={trigger}
            setValue={setValue}
            remove={handlePackageRemove}
            onChange={({ fieldKey }) => handleValueUpdate(fieldKey)}
          />
        ))}

        <button className={styles.addPackageButton} onClick={handleAddPackage}>
          + Add another piece group
        </button>
      </div>
    </div>
  );
});
