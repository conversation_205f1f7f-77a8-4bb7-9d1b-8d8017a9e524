# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL = "postgresql://johndoe:randompassword@localhost:5432/mydb?schema=public"

OPENAI_API_KEY = ""

SERVER_PORT = "3001"

SESSION_SECRET = ""
SESSION_EXPIRE_TIME_DAYS = "30";
