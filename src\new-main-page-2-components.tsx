import {
  Checkbox,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select as MuiSelect,
  styled,
  SxProps,
  Theme,
  SelectChangeEvent,
  TextField as Mui<PERSON><PERSON>t<PERSON><PERSON>,
  IconButton,
  Container,
  Link,
} from "@mui/material";
import styles from "./new-main-page-2-components.module.css";
import { HTMLInputTypeAttribute } from "react";
import { Icon } from "./features/create-requests/assets";
import useDisableMouseWheel from "./features/create-requests/utils/useDisableMouseWheel";
import { Lists } from "./shared";
import { normalizeNumber, normalizeNumberToTwoDecimals } from "./lib/math";

export const CustomizedInputLabel = styled(InputLabel) <{
  fontWeight?: number | string;
}>`
  &.MuiInputLabel-shrink {
    color: black;
    font-weight: ${({ fontWeight }) => fontWeight || 700};
    font-size: 12px;
    margin-top: 4px;
  }
`;

export const CustomizedTextField = styled(MuiTextField)`
  & .MuiOutlinedInput-root {
    border-radius: 0;
    font-size: 12px;
    min-height: 40px;
  }

  & .MuiInput-input {
    font-size: 12px;
  }

  & label.MuiInputLabel-shrink {
    color: black;
    font-weight: 700;
    font-size: 12px;
    margin-top: 4px;
  }

  & .Mui-focused .MuiOutlinedInput-notchedOutline {
    border: 2px solid green !important;
  }

  & .MuiFormHelperText-root {
    margin: 0;
    font-size: 10px;
    margin-top: 4px;
  }
`;

const CheckmarkBox = styled("span")`
  display: inline-block;
  box-sizing: content-box;
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
  width: 16px;
  height: 16px;
  border: 1px solid #70b57d;
  background-color: white;
  transition: all 0.2s ease;
`;

const CheckedCheckmarkBox = styled(CheckmarkBox)`
  background-color: white !important;
  width: 16px;
  height: 16px;

  &::before {
    content: "";
    background-image: url("/assets/checkedIcon.svg");
    display: block;
    width: 16px;
    height: 16px;
  }
`;

export const CustomCheckbox = ({
  label,
  checked,
  onChange,
  formName,
}: {
  checked: boolean;
  formName?: string;
  onChange?: (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean
  ) => void;
  label?: string;
}) => {
  return (
    <div className={styles.mainCheckboxContainer}>
      <Checkbox
        onChange={onChange}
        disableRipple
        checked={checked}
        checkedIcon={<CheckedCheckmarkBox />}
        name={formName}
        icon={<CheckmarkBox />}
        sx={{
          padding: 0,
        }}
      />
      {label}
    </div>
  );
};

export const SelectWithLabel = ({
  label,
  options,
  additionalStyling,
  value,
  placeholder,
  multiple,
  onChange,
  maxWidth,
  error,
}: {
  label: string;
  options: readonly string[];
  additionalStyling?: SxProps<Theme> | undefined;
  value: string | string[] | null;
  placeholder?: string;
  multiple?: boolean;
  onChange: (e: SelectChangeEvent<string | string[]>) => void;
  maxWidth?: number;
  error?: Error;
}) => {
  return (
    <FormControl
      fullWidth
      sx={{
        flexGrow: 1,
        maxWidth: maxWidth,
        ...additionalStyling,
      }}
      error={!!error}
      size="small"
    >
      <CustomizedInputLabel
        shrink
        id={`${label}-label`}
        sx={{ left: "-15px", marginBottom: "8px" }}
        fontWeight={500}
      >
        {label}
      </CustomizedInputLabel>
      <MuiSelect
        labelId={`${label}-label`}
        id={`${label}-label-select`}
        fullWidth
        multiple={multiple}
        value={value ?? ""}
        onChange={onChange}
        displayEmpty
        renderValue={(selected) => {
          if (selected?.length === 0 || !selected) {
            return <span>{placeholder}</span>;
          }

          return Array.isArray(selected) ? selected.join(", ") : selected;
        }}
        sx={{
          height: "40px",
          borderRadius: "0",
          fontSize: "12px",
          " &.Mui-focused .MuiOutlinedInput-notchedOutline": {
            border: "2px solid green",
          },
          marginTop: "8px",
          marginBottom: "4px",
        }}
        MenuProps={{
          disableScrollLock: true,
        }}
        slotProps={{
          input: {
            sx: {
              textTransform: "capitalize",
            },
          },
        }}
      >
        {!multiple && (
          <MenuItem
            sx={{
              fontSize: "12px",
            }}
          >
            <em>None</em>
          </MenuItem>
        )}
        {options.map((item) => (
          <MenuItem
            key={item}
            value={item}
            sx={{
              textTransform: "capitalize",
            }}
          >
            <CustomCheckbox
              formName={item}
              checked={
                Array.isArray(value)
                  ? value?.includes(item) ?? false
                  : value === item
              }
              label={item}
            />
          </MenuItem>
        ))}
      </MuiSelect>

      {error && (
        <FormHelperText error> {error.message}</FormHelperText>
      )}
    </FormControl>
  );
}

export const Select = ({
  label,
  options,
  styling,
  value,
  onChange,
}: {
  label: string;
  options: readonly string[];
  styling?: SxProps<Theme> | undefined;
  value: string | string[] | null;
  onChange: (e: SelectChangeEvent<string | string[]>) => void;
}) => {
  return (
    <FormControl
      sx={{
        flexBasis: "160px",
        flexGrow: 1,
        ...styling,
      }}
      size="small"
    >
      <CustomizedInputLabel>{label}</CustomizedInputLabel>
      <MuiSelect
        labelId={`${label}-label`}
        fullWidth
        label={label}
        value={value ?? ""}
        onChange={onChange}
        sx={{
          height: "40px",
          borderRadius: "0",
          fontSize: "12px",
          " &.Mui-focused .MuiOutlinedInput-notchedOutline": {
            border: "2px solid green",
          },
        }}
        MenuProps={{
          disableScrollLock: true,
        }}
        slotProps={{
          input: {
            sx: {
              textTransform: "capitalize",
            },
          },
        }}
      >
        <MenuItem
          sx={{
            fontSize: "12px",
          }}
        >
          <em>None</em>{" "}
        </MenuItem>
        {options.map((item) => (
          <MenuItem
            key={item}
            value={item}
            sx={{
              textTransform: "capitalize",
              fontSize: "12px",
            }}
          >
            {item}
          </MenuItem>
        ))}
      </MuiSelect>
    </FormControl>
  );
};

export const AddressInfo = ({
  label,
  checkboxLabel,
  isChecked,
  setIsChecked,
  city,
  setCity,
  cityError,
  address,
  setAddress,
  addressError,
  zipcode,
  setZipcode,
}: {
  label: string;
  checkboxLabel: string;

  isChecked: boolean;
  setIsChecked: (isChecked: boolean) => void;

  city: string | null;
  setCity: (city: string) => void;
  cityError?: string;

  address: string | null;
  setAddress: (address: string) => void;
  addressError?: string;

  zipcode: string | null;
  setZipcode: (zipcode: string) => void;
}) => {
  return (
    <div className={styles.addressInfoContainer}>
      <div className={styles.labelContainer}>
        <div>{label}</div>

        <CustomCheckbox
          label={checkboxLabel}
          checked={isChecked}
          onChange={(e) => setIsChecked(e.target.checked)}
        />
      </div>
      <div className={styles.firstLineContainer}>
        <CustomizedTextField
          label="City"
          size="small"
          value={city ?? ""}
          onChange={(e) => setCity(e.target.value)}
          sx={{
            maxWidth: 188,
            flexShrink: 1,
            flexGrow: 2,
          }}
          error={!!cityError}
          helperText={cityError}
        />

        <FormControl>
          <CustomizedTextField
            label="ZIP code"
            size="small"
            value={zipcode ?? ""}
            onChange={(e) => setZipcode(e.target.value)}
            sx={{ maxWidth: "100px", flexShrink: 2 }}
          />
        </FormControl>
      </div>
      <CustomizedTextField
        fullWidth
        label="Address"
        size="small"
        value={address ?? ""}
        onChange={(e) => setAddress(e.target.value)}
        sx={{ flexGrow: 1 }}
        error={!!addressError}
        helperText={addressError}
      />
    </div>
  );
};

export const TextField = ({
  locked = false,
  multiline,
  type,
  onBlur,
  additionalStyles,

  onChange,
  error,

  ...data
}: {
  locked?: boolean;
  multiline?: boolean;
  type?: HTMLInputTypeAttribute;
  onBlur?: () => void;
  additionalStyles?: SxProps<Theme> | undefined;

  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;

  expectedValue?: string | number;

  error?: string;
}) => {
  const inputRef = useDisableMouseWheel();

  const value = type === "number" ? normalizeNumberToTwoDecimals(data.value) : data.value;
  const expectedValue = data.expectedValue
    ? type === "number" ? normalizeNumberToTwoDecimals(data.expectedValue) : data.expectedValue
    : undefined;

  return (
    <>
      <CustomizedTextField
        ref={inputRef}
        multiline={multiline}
        fullWidth
        onBlur={onBlur}
        margin="dense"
        type={type}
        value={value}
        onChange={onChange}
        size="small"
        sx={{
          "& .MuiInputBase-input": { textAlign: "center" },
          ...additionalStyles,
        }}
        slotProps={{
          input: {
            endAdornment: locked ? <Icon.Lock /> : undefined,
            readOnly: locked,
          },
        }}
        error={!!error || expectedValue !== undefined && value !== expectedValue}
        helperText={error || expectedValue !== undefined && value !== expectedValue && (
          <>
            <span>Expected: {String(expectedValue)}</span>
            <br />
            <Link
              color="error"
              component="button"
              onClick={() => onChange({
                target: {
                  value: String(expectedValue),
                  valueAsNumber: Number(expectedValue),
                },
              } as unknown as React.ChangeEvent<HTMLInputElement>)}
            >
              Apply
            </Link>
          </>
        )}
      />
    </>
  );
};

export const TextFieldWithLabel = ({
  label,
  locked = false,
  maxWidth,
  multiline,
  additionalStyling,
  handleOnBlur,
  type,

  value,
  onChange,

  expectedValue,

  error,
}: {
  label: string;
  maxWidth?: number;
  multiline?: boolean;
  additionalStyling?: SxProps<Theme>;
  handleOnBlur?: () => void;
  type?: HTMLInputTypeAttribute;

  locked?: boolean;

  value: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;

  expectedValue?: string | number;

  error?: string;
}) => {
  return (
    <FormControl>
      <CustomizedInputLabel
        shrink
        sx={{ left: "-13px" }}
        fontWeight={500}
      >
        {label}
      </CustomizedInputLabel>
      <TextField
        multiline={multiline}
        onBlur={handleOnBlur}
        type={type}
        locked={locked}
        value={value}
        onChange={(...args) => onChange?.(...args)}
        expectedValue={expectedValue}
        additionalStyles={{
          flexShrink: 1,
          maxWidth: maxWidth,
          ...additionalStyling,
        }}
        error={error}
      />
    </FormControl>
  );
};

export const TextFieldWithLabelWithMultipleValues = ({
  label,
  locked = false,
  maxWidth,
  multiline,
  additionalStyling,
  handleOnBlur,
  type,

  values,
}: {
  label: string;
  maxWidth?: number;
  multiline?: boolean;
  additionalStyling?: SxProps<Theme>;
  handleOnBlur?: () => void;
  type?: HTMLInputTypeAttribute;

  locked?: boolean;

  values: readonly {
    value: string | number;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    expectedValue?: string | number;
  }[];
}) => {
  return (
    <FormControl>
      <CustomizedInputLabel
        shrink
        sx={{ left: "-13px" }}
        fontWeight={500}
      >
        {label}
      </CustomizedInputLabel>
      <Container
        sx={{
          display: "flex",
          gap: "8px",
          padding: 0,
          flexShrink: 1,
          maxWidth: maxWidth,
        }}
        maxWidth={false}
        disableGutters
      >
        {values.map(({ value, onChange, expectedValue }, index) => (
          <TextField
            key={index}
            multiline={multiline}
            onBlur={handleOnBlur}
            type={type}
            locked={locked}
            value={value}
            onChange={onChange}
            expectedValue={expectedValue}
            additionalStyles={{
              flexShrink: 1,
              maxWidth: maxWidth,
              ...additionalStyling,
            }}
          />
        ))}
      </Container>
    </FormControl>
  );
};

export type Package = {
  quantity: number;
  volume: number;
  weight: number;
  length: number;
  width: number;
  height: number;
  type: typeof Lists.packageTypes[number] | null;
  isStackable: boolean;
};

export const PackageItem = ({
  item,
  onChange,
  onRemove,
}: {
  item: Package;
  onChange: (item: Package) => void;
  onRemove: () => void;
}) => {
  return (
    <div className={styles.packageInputsContainer}>
      <TextFieldWithLabel
        label="Piece (pcs)"
        maxWidth={188}
        type="number"
        value={item.quantity}
        onChange={(e) => onChange({ ...item, quantity: normalizeNumber(e.target.value) })}
      />

      <TextFieldWithLabelWithMultipleValues
        label="Dimensions (cm)"
        maxWidth={290}
        type="number"
        values={[
          {
            value: item.length,
            onChange: (e) => onChange({ ...item, length: normalizeNumber(e.target.value) }),
          },
          {
            value: item.width,
            onChange: (e) => onChange({ ...item, width: normalizeNumber(e.target.value) }),
          },
          {
            value: item.height,
            onChange: (e) => onChange({ ...item, height: normalizeNumber(e.target.value) }),
          },
        ]}
      />

      <TextFieldWithLabel
        label="Volume (m³)"
        type="number"
        maxWidth={86}
        value={item.length * item.width * item.height / 1e6}
        onChange={(e) => onChange({ ...item, volume: normalizeNumber(e.target.value) })}
      />

      <TextFieldWithLabel
        type="number"
        label="Weight (kg)"
        maxWidth={89}
        value={item.weight}
        onChange={(e) => onChange({ ...item, weight: normalizeNumber(e.target.value) })}
      />

      <SelectWithLabel
        label="Package type"
        options={Lists.packageTypes}
        maxWidth={187}
        placeholder="Choose package"
        value={item.type}
        onChange={(e) => onChange({ ...item, type: e.target.value as typeof Lists.packageTypes[number] })}
      />

      <div className={styles.packageAlignContainer}>
        <CustomCheckbox
          label="non stackable"
          checked={!item.isStackable}
          onChange={(e) => onChange({ ...item, isStackable: !e.target.checked })}
        />
      </div>

      <div className={styles.packageAlignContainer}>
        <IconButton onClick={onRemove}>
          <Icon.Trash />
        </IconButton>
      </div>
    </div>
  );
};
