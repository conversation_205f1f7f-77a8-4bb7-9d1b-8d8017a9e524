import type { PrismaClient, RequestStatus } from "@prisma/client";

import { Injectable } from "../utils";
import { ForbiddenSignalError, NotFoundSignalError } from "../error/http";

export class RequestService extends Injectable {
    private readonly prisma!: PrismaClient;

    async create(dto: {
        userId: string;

        origin: string;
        destination: string;

        service: string;

        quantity: number;
        weight: number;
        volume: number;
        chargeableWeight: number;
    }) {
        const request = await this.prisma.$transaction(async trx => {
            const userRequestCounter = await trx.userRequestCounter.upsert({
                where: {
                    userId: dto.userId,
                },
                update: {
                    count: {
                        increment: 1,
                    },
                },
                create: {
                    userId: dto.userId,
                    count: 1,
                },
            });

            return await this.prisma.request.create({
                data: {
                    userId: dto.userId,

                    ordinal: userRequestCounter.count,

                    origin: dto.origin,
                    destination: dto.destination,

                    service: dto.service,

                    quantity: dto.quantity,
                    weight: dto.weight,
                    volume: dto.volume,
                    chargeableWeight: dto.chargeableWeight,
                },
            });
        });

        return {
            id: request.id,
        };
    }

    async updateStatus(dto: {
        userId: string;

        id: string;
        status: RequestStatus;
    }) {
        const request = await this.prisma.request.findUniqueOrThrow({
            where: {
                id: dto.id,
            },
        });

        if (request.userId !== dto.userId) {
            throw new ForbiddenSignalError("not author");
        }

        await this.prisma.request.update({
            where: { id: dto.id },
            data: {
                status: dto.status,
            },
        });
    }

    async getMany(dto: {
        userId: string;

        searchQuery?: string;

        origin?: string;
        destination?: string;

        service?: string;

        from?: Date;
        to?: Date;
    }) {
        const requests = await this.prisma.request.findMany({
            where: {
                userId: dto.userId,

                origin: dto.origin,
                destination: dto.destination,

                service: dto.service,

                createdAt: {
                    gte: dto.from,
                    lte: dto.to,
                },

                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return requests;
    }

    async getAvailableLocations(dto: {
        userId: string;
    }) {
        const [
            origins,
            destinations,
        ] = await Promise.all([
            this.prisma.request.findMany({
                where: {
                    userId: dto.userId,
                    deletedAt: null,
                },
                select: {
                    origin: true,
                },
                distinct: ["origin"],
            })
                .then(requests => requests.map(request => request.origin)),

            this.prisma.request.findMany({
                where: {
                    userId: dto.userId,
                    deletedAt: null,
                },
                select: {
                    destination: true,
                },
                distinct: ["destination"],
            })
                .then(requests => requests.map(request => request.destination)),
        ]);

        return {
            origins,
            destinations,
        };
    }

    async softDelete(dto: {
        id: string;
        userId: string;
    }) {
        await this.prisma.$transaction(async trx => {
            const request = await trx.request.findUnique({
                where: {
                    id: dto.id,
                },
            });

            if (!request) {
                throw new NotFoundSignalError("request");
            }

            if (request.userId !== dto.userId) {
                throw new ForbiddenSignalError("not author");
            }

            await trx.request.update({
                where: {
                    id: dto.id,
                },
                data: {
                    deletedAt: new Date(),
                },
            });
        });

        return true;
    }

    async hardDelete(dto: {
        id: string;
    }) {
        await this.prisma.request.delete({
            where: {
                id: dto.id,
            },
        });

        return true;
    }
}
