import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./main.css";
import { createTheme, ThemeProvider } from "@mui/material";
import { BrowserRouter, Route, Routes } from "react-router";
import { AuthLayout } from "./app/(auth)/layout.tsx";
import { SignInPage } from "./app/(auth)/sign-in/page.tsx";
import { SignUpPage } from "./app/(auth)/sign-up/page.tsx";
import { ResetPasswordPage } from "./app/(auth)/reset-password/page.tsx";
import { IndexLayout } from "./app/(index)/layout.tsx";
import { RequestsPage } from "./app/(index)/requests/page.tsx";
import { NewMainPage2 } from "./new-main-page-2.tsx";

const theme = createTheme({
  typography: {
    fontFamily: "'YS Text', sans-serif",
  },
  palette: {
    primary: {
      main: 'rgba(112, 181, 125, 1)',
    }
  }
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ThemeProvider theme={theme}>
      <BrowserRouter>
        <Routes>
          {/* <Route index element={<App />} /> */}
          <Route index element={<NewMainPage2 />} />

          <Route element={<IndexLayout />}>
            {/* <Route path="new-main" element={<NewMainPage />} /> */}
            <Route path="requests" element={<RequestsPage />} />
          </Route>

          <Route element={<AuthLayout />}>
            <Route path="sign-in" element={<SignInPage />} />
            <Route path="sign-up" element={<SignUpPage />} />
            <Route path="reset-password" element={<ResetPasswordPage />} />
          </Route>
        </Routes>
      </BrowserRouter>
    </ThemeProvider>
  </StrictMode>
);
