import { <PERSON><PERSON>, Circular<PERSON><PERSON>ress, <PERSON>, styled, <PERSON>u, <PERSON>uI<PERSON>, IconButton, Typography } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { CustomAlert, Main, Sidebar } from "./components";
import { Icon } from "./features/create-requests/assets";
import styles from "./new-main-page.module.css";

import {
  Main as MainContentMain,
  CopyButton,
} from "./features/create-requests/components"
import { Api, Lists } from "./shared";
import { SERVER_URL } from "./features/create-requests/utils/consts";
import { ShippingInfo } from "./features/create-requests/types/shippingTypes";
import { Select, SelectWithLabel, AddressInfo, CustomizedTextField, TextFieldWithLabel, PackageItem, Package, TextFieldLabelContainer, PlainTextField } from "./new-main-page-2-components";
import { normalizeNumber } from "./lib/math";
import { getLocalStorageUser, removeLocal<PERSON>torageUser, LocalStorageUser } from "./lib/user";
import { Requester } from "./lib/requester";

export const VALUE_SHOULD_BE_BIGGER_THAN_0 = "Value should be bigger than 0";
export const REQUIRED_WHEN_INCOTERMS_IS_EXW = "Required when incoterms is EXW";
export const REQUIRED_FOR_DAP_DDP_DPU_INCOTERMS = "required for DAP or DDP or DPU incoterms";
export const REQUIRED = "Required";

const StyledButton = styled(Button)({
  color: "white",
  fontSize: "12px",
  lineHeight: "16px",
  textTransform: "none",
  fontWeight: "400",
  borderRadius: "0",
  height: "40px",
});

export type TResultText = {
  title: string;
  mainContent: string;
};

function mapResultToNewFormat(data: ShippingInfo): {
  service: typeof Lists.services[number] | null;
  incoterms: typeof Lists.incoterms[number] | null;

  origin: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isPickupRequired: boolean;
  };

  destination: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isDeliveryRequired: boolean;
  };

  descriptionOfGoods: string | null;
  hsCodes: string[];

  costOfGoods: number | null;
  currency: string | null;

  additionalServices: typeof Lists.additionalServices[number][];
  dangerousGoods: typeof Lists.dangerousGoods[number][];

  packages: {
    quantity: number | null;
    volume: number | null;
    weight: number | null;
    length: number | null;
    width: number | null;
    height: number | null;
    type: typeof Lists.packageTypes[number] | null;
    isStackable: boolean;
  }[];
} {
  return {
    service: data.service_type ? data.service_type as typeof Lists.services[number] : null,
    incoterms: data.incoterms ? data.incoterms as typeof Lists.incoterms[number] : null,

    origin: {
      country: data.origin ?? null,
      city: data.pickup?.city ?? null,
      address: data.pickup?.address ?? null,
      zipcode: data.pickup?.zip_code ?? null,
      isPickupRequired: Boolean(data.pickup?.is_needed),
    },

    destination: {
      country: data.destination_country ?? null,
      city: data.delivery?.city ?? null,
      address: data.delivery?.address ?? null,
      zipcode: data.delivery?.zip_code ?? null,
      isDeliveryRequired: Boolean(data.delivery?.is_needed),
    },

    descriptionOfGoods: data.additional_details?.description_of_goods ?? null,
    hsCodes: data.additional_details?.hs_codes ? [String(data.additional_details.hs_codes)] : [],

    costOfGoods: data.additional_details?.costs_of_goods ?? null,
    currency: data.additional_details?.currency ?? null,

    additionalServices: data.additional_details?.selected_services?.filter(Boolean).map(service => service as typeof Lists.additionalServices[number]) ?? [],
    dangerousGoods: data.additional_details?.dangerous_goods?.filter(Boolean).map(service => service as typeof Lists.dangerousGoods[number]) ?? [],

    packages: data.details?.map(detail => ({
      quantity: detail.piece,
      volume: detail.dimension?.volume ?? null,
      weight: detail.dimension?.weight ?? null,
      length: detail.dimension?.length ?? null,
      width: detail.dimension?.width ?? null,
      height: detail.dimension?.height ?? null,
      type: detail.package_type ? detail.package_type as typeof Lists.packageTypes[number] : null,
      isStackable: Boolean(detail.dimension?.is_stackable),
    })) ?? [],
  }
}

function getResultTitle(data: {
  ordinal: number | null;
  incoterms: typeof Lists.incoterms[number] | null;
  originCity: string | null;
  originCountry: string | null;
  destinationCity: string | null;
  destinationCountry: string | null;
  totalWeight: number;
  totalVolume: number;
}) {
  const { ordinal, incoterms, originCity, originCountry, destinationCity, destinationCountry } = data;

  const itinerary = incoterms || originCountry || destinationCountry
    ? `${incoterms ? incoterms + " " : ""}${originCity ?? originCountry ?? "unknown"} – ${destinationCity ?? destinationCountry ?? "unknown"}`
    : "";

  const weight = data.totalWeight > 0 ? `${data.totalWeight.toFixed(2)} kgs` : "";
  const volume = data.totalVolume > 0 ? `${data.totalVolume.toFixed(2)} cbm` : "";

  const title = [itinerary, weight, volume].filter(Boolean).join(", ");

  return title.length
    ? ["Quotation", ordinal !== null ? `IN${String(ordinal).padStart(3, "0")}` : "", title].join(" ")
    : "";
}

function getResultContent(data: {
  service: typeof Lists.services[number] | null;
  incoterms: typeof Lists.incoterms[number] | null;
  origin: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isPickupRequired: boolean;
  };
  destination: {
    country: string | null;
    city: string | null;
    address: string | null;
    zipcode: string | null;
    isDeliveryRequired: boolean;
  };
  descriptionOfGoods: string | null;
  hsCodes: string | null;
  additionalServices: typeof Lists.additionalServices[number][];
  dangerousGoods: typeof Lists.dangerousGoods[number][];

  packages: {
    quantity: number;
    volume: number;
    weight: number;
    length: number;
    width: number;
    height: number;
    type: typeof Lists.packageTypes[number] | null;
    isStackable: boolean;
  }[];
}) {
  const {
    service,
    incoterms,
    origin,
    destination,
    descriptionOfGoods,
    hsCodes,
    additionalServices,
    dangerousGoods,
    packages,
  } = data;

  const requestedService = service ? `Requested service: ${service}` : "";

  const itinerary = incoterms || origin.country || destination.country
    ? `${incoterms ? incoterms + " " : ""}${origin.country ?? "unknown"} – ${destination.country ?? "unknown"}`
    : "";

  const pickupAddress = origin.isPickupRequired
    ? `Pickup address: ${[origin.zipcode, origin.address, origin.city].filter(Boolean).join(", ")}`
    : "";

  const deliveryAddress = destination.isDeliveryRequired
    ? `Delivery address: ${[destination.zipcode, destination.address, destination.city].filter(Boolean).join(", ")}`
    : "";

  const hsCodesText = hsCodes ? `HS codes: ${hsCodes}` : "";
  const descriptionOfGoodsText = descriptionOfGoods ? `Description of goods: ${descriptionOfGoods}` : "";

  const otherDetails = [
    additionalServices.length ? `Additional services: ${additionalServices.join(", ")}` : "",
    dangerousGoods.length ? `Dangerous goods: ${dangerousGoods.join(", ")}` : "",
  ]
    .filter(Boolean)
    .join("\n");

  const route = [requestedService, itinerary, pickupAddress, deliveryAddress]
    .filter(Boolean)
    .join("\n");

  const itemsText = packages.map(pkg => {
    const pieces = pkg.quantity > 1 ? `${pkg.quantity} pcs. -` : `${pkg.quantity} pc. -`;

    const volume = pkg.volume > 0 ? `${pkg.volume.toFixed(2)} cbm` : "";
    const weight = pkg.weight > 0 ? `${pkg.weight.toFixed(2)} kg` : "";

    const dimensions = [
      pkg.length > 0 ? Math.floor(pkg.length) : "",
      pkg.width > 0 ? Math.floor(pkg.width) : "",
      pkg.height > 0 ? Math.floor(pkg.height) : "",
    ]
      .filter(Boolean)
      .join("*");

    const stackable = pkg.isStackable ? null : "not stackable";
    const packageType = pkg.type ? `package type: ${pkg.type}` : "";

    return `${pieces} per unit ${[
      weight,
      dimensions ? `${dimensions} cm` : "",
      volume,
      stackable,
      packageType,
    ]
      .filter(Boolean)
      .join(", ")}`;
  }).join("\n");

  const packageDetailsText = itemsText.length
    ? `Package details:\n ${itemsText}`
    : "";

  const goodsInfo = [descriptionOfGoodsText, hsCodesText, otherDetails]
    .filter(Boolean)
    .join("\n");

  const content = [route, packageDetailsText, goodsInfo]
    .filter(Boolean)
    .join("\n\n");

  return content.length
    ? `Dear colleagues, \nPlease provide your quotation on request: \n\n ${content}`
    : "";
}

export function NewMainPage2() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const [user, setUser] = useState<LocalStorageUser | null | undefined>(undefined);
  const [profileMenuAnchor, setProfileMenuAnchor] = useState<null | HTMLElement>(null);

  useEffect(() => {
    setUser(getLocalStorageUser());
  }, []);

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setProfileMenuAnchor(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setProfileMenuAnchor(null);
  };

  const handleLogout = async () => {
    try {
      await Requester.post("/auth/sign-out", {});
      removeLocalStorageUser();
      setUser(null);
      handleProfileMenuClose();
    } catch (error) {
      console.error("Logout failed:", error);
      removeLocalStorageUser();
      setUser(null);
      handleProfileMenuClose();
    }
  };

  const [textError, setTextError] = useState<string | null>(null);

  const [id, setId] = useState<string | null>(null);

  const [ordinal, setOrdinal] = useState<number | null>(null);

  const [text, setText] = useState("");

  const [resultTitle, setResultTitle] = useState("");
  const [resultContent, setResultContent] = useState("");

  const [service, setService] = useState<typeof Lists.services[number] | null>(null);
  const [incoterms, setIncoterms] = useState<typeof Lists.incoterms[number] | null>(null);

  const [origin, setOrigin] = useState<string | null>(null);
  const [destination, setDestination] = useState<string | null>(null);

  const [isPickupRequired, setIsPickupRequired] = useState(false);
  const [pickupCity, setPickupCity] = useState<string | null>(null);
  const [pickupAddress, setPickupAddress] = useState<string | null>(null);
  const [pickupZipcode, setPickupZipcode] = useState<string | null>(null);

  const [isDeliveryRequired, setIsDeliveryRequired] = useState(false);
  const [deliveryCity, setDeliveryCity] = useState<string | null>(null);
  const [deliveryAddress, setDeliveryAddress] = useState<string | null>(null);
  const [deliveryZipcode, setDeliveryZipcode] = useState<string | null>(null);

  const [totalQuantity, setTotalQuantity] = useState<number>(0);
  const [expectedTotalQuantity, setExpectedTotalQuantity] = useState<number>(0);

  const [totalWeight, setTotalWeight] = useState<number>(0);
  const [expectedTotalWeight, setExpectedTotalWeight] = useState<number>(0);

  const [totalVolume, setTotalVolume] = useState<number>(0);
  const [expectedTotalVolume, setExpectedTotalVolume] = useState<number>(0);

  const [totalDensity, setTotalDensity] = useState<number>(0);

  const [descriptionOfGoods, setDescriptionOfGoods] = useState<string | null>(null);
  const [hsCodes, setHsCodes] = useState<string | null>(null);

  const [costOfGoods, setCostOfGoods] = useState<number>(0);
  const [currency, setCurrency] = useState<string | null>(null);

  const [additionalServices, setAdditionalServices] = useState<typeof Lists.additionalServices[number][]>([]);
  const [dangerousGoods, setDangerousGoods] = useState<typeof Lists.dangerousGoods[number][]>([]);

  const [chargeableWeight, setChargeableWeight] = useState<number>(0);

  useEffect(() => {
    if (service === "air") {
      setChargeableWeight(Math.max(totalWeight, totalVolume * 167, 0));
    }
  }, [service, totalWeight, totalVolume]);

  const [packages, setPackages] = useState<{
    quantity: number;
    volume: number;
    weight: number;
    length: number;
    width: number;
    height: number;
    type: typeof Lists.packageTypes[number] | null;
    isStackable: boolean;
  }[]>([]);

  useEffect(() => {
    const totalQuantity = packages.reduce((acc, p) => acc + (p.quantity ?? 1), 0);
    setTotalQuantity(totalQuantity);
    setExpectedTotalQuantity(totalQuantity);

    const totalWeight = packages.reduce((acc, p) => acc + (p.weight ?? 0), 0);
    const totalVolume = packages.reduce((acc, p) => {
      if (p.volume) return acc + p.volume;

      return acc + (p.length * p.width * p.height) / 1e6;
    }, 0);

    setTotalWeight(totalWeight);
    setExpectedTotalWeight(totalWeight);

    setTotalVolume(totalVolume);
    setExpectedTotalVolume(totalVolume);

    setTotalDensity(totalVolume > 0 ? totalWeight / totalVolume : 0);
  }, [packages]);

  function paste() {
    navigator.clipboard.readText().then(text => {
      setText(text);
    });
  }

  const [isPendingSend, setIsPendingSend] = useState(false);

  const abortControllerRef = useRef<AbortController | null>(null);

  async function send() {
    setIsPendingSend(true);

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    let isAborted = false;

    abortControllerRef.current.signal.addEventListener("abort", () => isAborted = true);

    const result = await fetch(`${SERVER_URL}/extract`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      signal: abortControllerRef.current.signal,
      body: JSON.stringify({
        text: text,
      }),
    });

    if (result.ok) {
      const data = mapResultToNewFormat(await result.json());

      setResultTitle(resultTitle);
      setResultContent(resultContent);

      setOrigin(data.origin.country);
      setDestination(data.destination.country);

      setService(data.service);
      setIncoterms(data.incoterms);

      setPickupCity(data.origin.city);
      setPickupAddress(data.origin.address);
      setPickupZipcode(data.origin.zipcode);
      setIsPickupRequired(data.origin.isPickupRequired);

      setDeliveryCity(data.destination.city);
      setDeliveryAddress(data.destination.address);
      setDeliveryZipcode(data.destination.zipcode);
      setIsDeliveryRequired(data.destination.isDeliveryRequired);

      setDescriptionOfGoods(data.descriptionOfGoods);
      setHsCodes(data.hsCodes.join(", "));

      setCostOfGoods(data.costOfGoods ?? 0);
      setCurrency(data.currency);

      setAdditionalServices(data.additionalServices);
      setDangerousGoods(data.dangerousGoods);

      const packages = data.packages.map(p => ({
        quantity: p.quantity ?? 1,
        volume: p.volume ?? 0,
        weight: p.weight ?? 0,
        length: p.length ?? 0,
        width: p.width ?? 0,
        height: p.height ?? 0,
        type: p.type,
        isStackable: p.isStackable,
      }));

      setPackages(packages);
    }
    else {
      setTextError(`Failed to send request: ${result.statusText}`);
    }

    if (!isAborted) {
      setIsPendingSend(false);
    }
  }

  const [isPendingSave, setIsPendingSave] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [isSaved, setIsSaved] = useState(false);

  async function save() {
    setIsPendingSave(true);

    console.dir(packages, { depth: null });

    const data = {
      text,
      service,
      incoterms,
      origin: {
        country: origin,
        city: pickupCity,
        address: pickupAddress,
        zipcode: pickupZipcode,
        isPickupRequired,
      },
      destination: {
        country: destination,
        city: deliveryCity,
        address: deliveryAddress,
        zipcode: deliveryZipcode,
        isDeliveryRequired,
      },
      quantity: totalQuantity,
      weight: totalWeight,
      volume: totalVolume,
      chargeableWeight,
      packages: packages.map(pkg => ({
        ...pkg,
        type: pkg.type && ({
          "Box": "box",
          "Crate": "crate",
          "Pallet": "pallet",
        } as const)[pkg.type],
      })),
      descriptionOfGoods,
      hsCodes,
      costOfGoods,
      currency,
      additionalServices,
      dangerousGoods,
    }

    const result = id
      ? await Requester.put<Api.Request2>(`/request/${id}`, data)
      : await Requester.post<Api.Request2>("/request", data);

    if (result.success) {
      setIsSaved(true);
    }
    else if (result.error) {
      setSaveError(result.error.message);
    }
    else {
      setSaveError(`Failed to save request: ${result.status}, ${result.body}`);
    }

    setIsPendingSave(false);
  }

  function addPackage() {
    setPackages([
      ...packages,
      {
        quantity: 1,
        volume: 0,
        weight: 0,
        length: 0,
        width: 0,
        height: 0,
        type: null,
        isStackable: true,
      },
    ]);
  }

  function changePackage(index: number, item: Package) {
    setPackages(packages.map((p, i) => i === index ? item : p));
  }

  function removePackage(index: number) {
    setPackages(packages.filter((_, i) => i !== index));
  }

  function reset() {
    setText("");

    setResultTitle("");
    setResultContent("");

    setOrigin(null);
    setDestination(null);

    setService(null);
    setIncoterms(null);

    setPickupCity(null);
    setPickupAddress(null);
    setPickupZipcode(null);
    setIsPickupRequired(false);

    setDeliveryCity(null);
    setDeliveryAddress(null);
    setDeliveryZipcode(null);
    setIsDeliveryRequired(false);

    setDescriptionOfGoods(null);
    setHsCodes(null);

    setCostOfGoods(0);
    setCurrency(null);

    setAdditionalServices([]);
    setDangerousGoods([]);

    setPackages([]);

    setTotalQuantity(0);
    setExpectedTotalQuantity(0);

    setTotalWeight(0);
    setExpectedTotalWeight(0);

    setTotalVolume(0);
    setExpectedTotalVolume(0);

    setTotalDensity(0);

    setChargeableWeight(0);
  }

  function getPickupCityError() {
    if (incoterms === "EXW" && !pickupCity) {
      return REQUIRED_WHEN_INCOTERMS_IS_EXW;
    }

    return undefined;
  }

  function getPickupAddressError() {
    if (incoterms === "EXW" && !pickupAddress) {
      return REQUIRED_WHEN_INCOTERMS_IS_EXW;
    }

    return undefined;
  }

  function getDeliveryCityError() {
    if (incoterms === "DAP" || incoterms === "DDP" || incoterms === "DPU" && !deliveryCity) {
      return REQUIRED_FOR_DAP_DDP_DPU_INCOTERMS;
    }

    return undefined;
  }

  function getDeliveryAddressError() {
    if (incoterms === "DAP" || incoterms === "DDP" || incoterms === "DPU" && !deliveryAddress) {
      return REQUIRED_FOR_DAP_DDP_DPU_INCOTERMS;
    }

    return undefined;
  }

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const id = searchParams.get("id");

    if (id) {
      async function loadExistingRequest() {
        const result = await Requester.get<Api.Request2>(`/request/${id}`);

        if (result.success) {
          const request = result.body;

          setOrdinal(request.ordinal);

          setId(id);

          setText(request.text);

          setOrigin(request.origin.country);
          setDestination(request.destination.country);

          setService(request.service);
          setIncoterms(request.incoterms);

          setPickupCity(request.origin.city);
          setPickupAddress(request.origin.address);
          setPickupZipcode(request.origin.zipcode);
          setIsPickupRequired(request.origin.isPickupRequired);

          setDeliveryCity(request.destination.city);
          setDeliveryAddress(request.destination.address);
          setDeliveryZipcode(request.destination.zipcode);
          setIsDeliveryRequired(request.destination.isDeliveryRequired);

          setDescriptionOfGoods(request.descriptionOfGoods);
          setHsCodes(request.hsCodes);

          setCostOfGoods(Number(request.costOfGoods ?? 0));
          setCurrency(request.currency);

          setAdditionalServices(request.additionalServices as typeof Lists.additionalServices[number][]);
          setDangerousGoods(request.dangerousGoods as typeof Lists.dangerousGoods[number][]);

          setPackages(request.packages.map(pkg => ({
            quantity: pkg.quantity,
            length: pkg.length,
            width: pkg.width,
            height: pkg.height,
            weight: pkg.weight,
            volume: pkg.length * pkg.width * pkg.height / 1e6,
            type: pkg.type && ({
              box: "Box",
              crate: "Crate",
              pallet: "Pallet",
            } as const)[pkg.type],
            isStackable: pkg.isStackable,
          })));

          setTotalQuantity(request.quantity);
          setExpectedTotalQuantity(request.quantity);

          setTotalWeight(request.weight);
          setExpectedTotalWeight(request.weight);

          setTotalVolume(request.volume);
          setExpectedTotalVolume(request.volume);

          setTotalDensity(request.volume > 0 ? request.weight / request.volume : 0);

          setChargeableWeight(request.chargeableWeight);
        }
      }

      loadExistingRequest();
    }
  }, []);

  useEffect(() => {
    // Calculate totals for title generation
    const totalWeight = packages.reduce((acc, p) => acc + (p.weight ?? 0), 0);
    const totalVolume = packages.reduce((acc, p) => {
      if (p.volume) return acc + p.volume;
      return acc + ((p.length ?? 0) * (p.width ?? 0) * (p.height ?? 0)) / 1e6;
    }, 0);

    const resultTitle = getResultTitle({
      ordinal,
      incoterms: incoterms,
      originCity: pickupCity,
      originCountry: origin,
      destinationCity: deliveryCity,
      destinationCountry: destination,
      totalWeight,
      totalVolume,
    });

    // Convert packages to the expected format for getResultContent
    const packagesForContent = packages.map(p => ({
      quantity: p.quantity ?? 1,
      volume: p.volume ?? 0,
      weight: p.weight ?? 0,
      length: p.length ?? 0,
      width: p.width ?? 0,
      height: p.height ?? 0,
      type: p.type,
      isStackable: p.isStackable,
    }));

    const resultContent = getResultContent({
      service,
      incoterms,
      origin: {
        country: origin,
        city: pickupCity,
        address: pickupAddress,
        zipcode: pickupZipcode,
        isPickupRequired,
      },
      destination: {
        country: destination,
        city: deliveryCity,
        address: deliveryAddress,
        zipcode: deliveryZipcode,
        isDeliveryRequired,
      },
      descriptionOfGoods: descriptionOfGoods,
      hsCodes: hsCodes,
      additionalServices: additionalServices,
      dangerousGoods: dangerousGoods,
      packages: packagesForContent,
    });

    setResultTitle(resultTitle);
    setResultContent(resultContent);
  }, [
    ordinal,
    service,
    incoterms,
    origin,
    pickupCity,
    pickupAddress,
    pickupZipcode,
    isPickupRequired,
    destination,
    deliveryCity,
    deliveryAddress,
    deliveryZipcode,
    isDeliveryRequired,
    descriptionOfGoods,
    hsCodes,
    additionalServices,
    dangerousGoods,
    packages,
  ]);

  useEffect(() => {
    if (incoterms === "EXW") {
      setIsPickupRequired(true);
    }

    if (incoterms === "DDP") {
      setIsDeliveryRequired(true);
    }
  }, [incoterms]);

  return (
    <>
      <Sidebar handleSidebarClose={() => setIsSidebarOpen(false)} open={isSidebarOpen} />
      <Main open={isSidebarOpen}>
        {/* header */}
        <div className={styles.header}>
          <button
            onClick={() => setIsSidebarOpen(true)}
            className={`${styles.base} ${isSidebarOpen ? styles.open : styles.visible
              }`}
          >
            <Icon.OpenSidebar />
          </button>
          <div className={styles.container}>
            <StyledButton
              sx={{
                backgroundColor: "rgba(112, 181, 125, 1)",
                width: "186px",
              }}
              onClick={reset}
              startIcon={<Icon.Plus />}
            >
              Create new request
            </StyledButton>
            <StyledButton
              sx={{
                width: "120px",
                backgroundColor: "#467c8d",
                color: "white",
              }}
              loading={isPendingSave}
              onClick={save}
              disabled={!user}
            >
              Save request
            </StyledButton>
            {user === null && (
              <StyledButton
                sx={{
                  border: "1px solid rgba(26, 26, 26, 1)",
                  width: "92px",
                  color: "black",
                }}
              >
                <Link href="/sign-in" style={{ textDecoration: "none", color: "black" }}>
                  Sign in
                </Link>
              </StyledButton>
            )}

            {user && (
              <>
                <IconButton
                  onClick={handleProfileMenuOpen}
                  sx={{
                    padding: 0,
                    "&:hover": {
                      backgroundColor: "transparent",
                    },
                  }}
                >
                  <Icon.Profile />
                </IconButton>
                <Menu
                  anchorEl={profileMenuAnchor}
                  open={Boolean(profileMenuAnchor)}
                  onClose={handleProfileMenuClose}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  sx={{
                    mt: 1,
                    "& .MuiPaper-root": {
                      minWidth: 200,
                      boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
                      borderRadius: "8px",
                    },
                  }}
                >
                  <MenuItem
                    sx={{
                      padding: "12px 16px",
                      borderBottom: "1px solid #f0f0f0",
                      "&:hover": {
                        backgroundColor: "transparent",
                      },
                      cursor: "default",
                    }}
                    disableRipple
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: "14px",
                        color: "#1B1D1F",
                        fontWeight: 500,
                      }}
                    >
                      {user.email}
                    </Typography>
                  </MenuItem>
                  <MenuItem
                    onClick={handleLogout}
                    sx={{
                      padding: "12px 16px",
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                      "&:hover": {
                        backgroundColor: "#f5f5f5",
                      },
                    }}
                  >
                    <Icon.Logout />
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: "14px",
                        color: "#EB4E3D",
                        fontWeight: 500,
                      }}
                    >
                      Logout
                    </Typography>
                  </MenuItem>
                </Menu>
              </>
            )}
          </div>
        </div>

        {/* main content */}
        <CustomAlert message={textError} severity="error" />
        <MainContentMain open={isSidebarOpen}>
          <div className={styles.contentContainer}>
            <div className={styles.textContainer}>
              <div className={styles.label}>
                Paste here the text of request for cargo transportation
              </div>

              <button
                onClick={send}
                disabled={isPendingSend || !text.trim().length}
                className={styles.sendButton}
              >
                {isPendingSend ? (
                  <CircularProgress
                    size={16}
                    style={{ position: "absolute", color: "white" }}
                  />
                ) : (
                  <Icon.Send />
                )}
              </button>

              <div className={styles.textInputsContainer}>
                <CustomAlert message={textError} severity="error" />
                <div className={styles.textIntoInputContainer}>
                  <div className={styles.textFieldContainer}>
                    <textarea
                      className={styles.textField}
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                    />
                  </div>

                  <Button
                    disableElevation
                    onClick={paste}
                    startIcon={<Icon.Paste />}
                    size="small"
                    variant="text"
                    sx={{
                      fontSize: "12px",
                      textTransform: "lowercase",
                      cursor: "pointer",
                      fontWeight: "400",
                      lineHeight: '16px',
                      marginTop: "5px",
                    }}
                  >
                    paste
                  </Button>
                </div>

                <div className={styles.resultTextContainer}>
                  <div className={styles.titleResult}>
                    <div style={{ flexShrink: 2, overflow: "auto" }}>{resultTitle}</div>
                    <CopyButton text={resultTitle} disabled={!resultTitle} />
                  </div>

                  <div className={styles.fullTextResultContainer}>
                    <div className={styles.fullTextResult}>{resultContent}</div>
                    <div className={styles.actionBtns}>
                      <CopyButton text={resultContent} disabled={!resultContent} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <CustomAlert message={saveError} severity="error" />
            <CustomAlert message={isSaved ? "Request saved successfully" : null} severity="success" />

            <div className={styles.addressesInfoContainer}>
              <div className={styles.deliveryInfoContainer}>
                <div>
                  <Select
                    label="Service"
                    options={Lists.services}
                    value={service}
                    onChange={(e) => setService(e.target.value as typeof Lists.services[number])}
                  />

                  <Select
                    label="Incoterms"
                    options={Lists.incoterms}
                    value={incoterms}
                    onChange={(e) => setIncoterms(e.target.value as typeof Lists.incoterms[number])}
                  />
                </div>

                <div>
                  <CustomizedTextField
                    label="Origin"
                    size="small"
                    value={origin ?? ""}
                    onChange={(e) => setOrigin(e.target.value)}
                    sx={{
                      flexGrow: 1,
                    }}
                  />

                  <CustomizedTextField
                    label="Destination"
                    size="small"
                    value={destination ?? ""}
                    onChange={(e) => setDestination(e.target.value)}
                    sx={{
                      flexGrow: 1,
                    }}
                  />
                </div>
              </div>

              <AddressInfo
                label="Pick-up address"
                checkboxLabel="yes, we need pick up"
                isChecked={isPickupRequired}
                setIsChecked={setIsPickupRequired}
                checkboxReadonly={incoterms === "EXW"}
                city={pickupCity}
                setCity={setPickupCity}
                zipcode={pickupZipcode}
                setZipcode={setPickupZipcode}
                address={pickupAddress}
                setAddress={setPickupAddress}

                cityError={getPickupCityError()}
                addressError={getPickupAddressError()}
              />

              <AddressInfo
                label="Delivery address"
                checkboxLabel="yes, we need address delivery"
                isChecked={isDeliveryRequired}
                setIsChecked={setIsDeliveryRequired}
                checkboxReadonly={incoterms === "DDP"}
                city={deliveryCity}
                setCity={setDeliveryCity}
                zipcode={deliveryZipcode}
                setZipcode={setDeliveryZipcode}
                address={deliveryAddress}
                setAddress={setDeliveryAddress}

                cityError={getDeliveryCityError()}
                addressError={getDeliveryAddressError()}
              />
            </div>

            {/* shipment summary */}
            <div className={styles.shipmentSummaryContainer}>
              <h3 className={styles.shipmentSummaryLabel}>Shipment Summary</h3>

              <div className={styles.inputsContainer}>
                <TextFieldWithLabel
                  label="Piece (pcs)"
                  type="number"
                  value={totalQuantity}
                  onChange={(e) => setTotalQuantity(normalizeNumber(e.target.value))}
                  expectedValue={expectedTotalQuantity}
                  error={totalQuantity === 0 ? VALUE_SHOULD_BE_BIGGER_THAN_0 : undefined}
                />
                <TextFieldWithLabel
                  label="Total weight (kg)"
                  type="number"
                  value={totalWeight}
                  onChange={(e) => setTotalWeight(normalizeNumber(e.target.value))}
                  expectedValue={expectedTotalWeight}
                  error={totalWeight === 0 ? VALUE_SHOULD_BE_BIGGER_THAN_0 : undefined}
                />
                <TextFieldWithLabel
                  label="Volume (m³)"
                  type="number"
                  value={totalVolume}
                  onChange={(e) => setTotalVolume(normalizeNumber(e.target.value))}
                  expectedValue={expectedTotalVolume}
                  error={totalVolume === 0 ? VALUE_SHOULD_BE_BIGGER_THAN_0 : undefined}
                />
                <TextFieldWithLabel
                  label="Density"
                  type="number"
                  locked
                  value={totalDensity}
                />

                {service === "air" && (
                  <TextFieldWithLabel
                    label="Chargeable weight"
                    type="number"
                    locked
                    value={chargeableWeight}
                  />
                )}
              </div>
            </div>

            {/* package details */}
            <div className={styles.packageDetailsContainer}>
              <h3>Package Details</h3>

              <div className={styles.packageDetailsSectionsContainer}>
                {packages.map((item, index) => (
                  <PackageItem
                    key={index}
                    item={item}
                    onChange={changePackage.bind(null, index)}
                    onRemove={removePackage.bind(null, index)}
                  />
                ))}

                <button className={styles.addPackageButton} onClick={addPackage}>
                  + Add another piece group
                </button>
              </div>
            </div>

            <div className={styles.additionalDetailsContainer}>
              <h3>Additional Details</h3>

              <div className={styles.inputsContainer}>
                <TextFieldWithLabel
                  label="Description of goods"
                  multiline={true}
                  additionalStyling={{
                    "& .MuiInputBase-input": { textAlign: "start" },
                  }}
                  value={descriptionOfGoods ?? ""}
                  onChange={(e) => setDescriptionOfGoods(e.target.value)}
                />
                <TextFieldWithLabel
                  label="HS codes"
                  value={hsCodes ?? ""}
                  onChange={(e) => setHsCodes(e.target.value)}
                />

                <TextFieldLabelContainer
                  label="Cost of goods"
                >
                  <PlainTextField
                    type="text"
                    value={currency ?? ""}
                    onChange={(e) => setCurrency(e.target.value.toUpperCase().slice(0, 3).replace(/[^A-Z]/g, "") || null)}
                    maxWidth={0.2}
                  />

                  <PlainTextField
                    type="number"
                    value={costOfGoods}
                    onChange={(e) => setCostOfGoods(normalizeNumber(e.target.value))}
                  />
                </TextFieldLabelContainer>

                <SelectWithLabel
                  options={Lists.additionalServices}
                  label="Additional services"
                  value={additionalServices}
                  onChange={(e) => setAdditionalServices(e.target.value as typeof Lists.additionalServices[number][])}
                  multiple
                  placeholder="Choose services"
                />

                <SelectWithLabel
                  options={Lists.dangerousGoods}
                  label="Dangerous Goods"
                  value={dangerousGoods}
                  onChange={(e) => setDangerousGoods(e.target.value as typeof Lists.dangerousGoods[number][])}
                  multiple
                  placeholder="Choose goods"
                />
              </div>
            </div>
          </div>
        </MainContentMain>
      </Main>
    </>
  );
}
